# 打浩然地鼠游戏

一个有趣的打地鼠游戏，使用浩然的照片作为地鼠形象！

## 游戏特点

- 🎯 经典打地鼠玩法
- 🖼️ 使用浩然照片作为地鼠形象
- ⏱️ 30秒限时挑战
- 🎨 精美的游戏界面
- 📱 响应式设计

## 如何开始游戏

1. 确保目录中有以下文件：
   - `index.html` - 游戏主页面
   - `game.js` - 游戏逻辑
   - `haoran.jpg` - 地鼠图片

2. 用浏览器打开 `index.html` 文件

3. 点击"开始游戏"按钮

4. 用鼠标点击出现的浩然地鼠来得分！

## 游戏规则

- 每击中一个地鼠得10分
- 游戏时间为30秒
- 尽可能获得高分！

## 文件说明

- `index.html` - 游戏界面和样式
- `game.js` - 游戏逻辑和交互
- `haoran.jpg` - 地鼠图片（已自动生成示例图片）
- `create_sample_image.py` - 用于创建示例图片的Python脚本

## 自定义图片

如果你想使用真实的浩然照片，只需将照片重命名为 `haoran.jpg` 并替换当前文件即可。

## 技术栈

- HTML5 Canvas
- JavaScript ES6+
- CSS3
- 纯前端实现，无需服务器

祝你游戏愉快！