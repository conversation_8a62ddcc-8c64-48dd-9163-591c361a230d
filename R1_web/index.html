<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打地鼠游戏 - 浩然版</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .game-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        
        .game-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            font-size: 20px;
            font-weight: bold;
        }
        
        .score, .time {
            color: #667eea;
        }
        
        #gameCanvas {
            border: 3px solid #667eea;
            border-radius: 10px;
            cursor: crosshair;
            background: #f0f8ff;
        }
        
        .controls {
            margin-top: 20px;
        }
        
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 10px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        button:hover {
            background: #764ba2;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .game-over {
            font-size: 24px;
            color: #e74c3c;
            margin-top: 20px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>🎯 打浩然地鼠游戏 🎯</h1>
        
        <div class="game-info">
            <div class="score">得分: <span id="score">0</span></div>
            <div class="time">时间: <span id="time">30</span>秒</div>
        </div>
        
        <canvas id="gameCanvas" width="600" height="400"></canvas>
        
        <div class="controls">
            <button id="startBtn" onclick="startGame()">开始游戏</button>
            <button id="resetBtn" onclick="resetGame()" disabled>重新开始</button>
        </div>
        
        <div class="game-over" id="gameOver">
            游戏结束！最终得分: <span id="finalScore">0</span>
        </div>
    </div>

    <script src="game.js"></script>
</body>
</html>