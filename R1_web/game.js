// 游戏变量
let canvas, ctx;
let score = 0;
let timeLeft = 30;
let gameActive = false;
let moles = [];
let moleImage;
let gameTimer;
let moleTimer;

// 地鼠洞位置
const holes = [
    {x: 100, y: 100},
    {x: 300, y: 100},
    {x: 500, y: 100},
    {x: 100, y: 250},
    {x: 300, y: 250},
    {x: 500, y: 250}
];

// 初始化游戏
function initGame() {
    canvas = document.getElementById('gameCanvas');
    ctx = canvas.getContext('2d');
    
    // 加载地鼠图片
    moleImage = new Image();
    moleImage.src = 'haoran.jpg';
    
    // 如果haoran.jpg不存在，使用备用图片
    moleImage.onerror = function() {
        console.log('haoran.jpg 未找到，使用备用图片');
        // 创建一个简单的地鼠图形
        createBackupMole();
    };
    
    // 绘制初始界面
    drawBackground();
    
    // 添加点击事件
    canvas.addEventListener('click', handleClick);
}

// 创建备用地鼠图形
function createBackupMole() {
    const backupCanvas = document.createElement('canvas');
    backupCanvas.width = 80;
    backupCanvas.height = 80;
    const backupCtx = backupCanvas.getContext('2d');
    
    // 绘制圆形地鼠
    backupCtx.fillStyle = '#8B4513';
    backupCtx.beginPath();
    backupCtx.arc(40, 40, 35, 0, Math.PI * 2);
    backupCtx.fill();
    
    // 绘制眼睛
    backupCtx.fillStyle = 'white';
    backupCtx.beginPath();
    backupCtx.arc(25, 30, 8, 0, Math.PI * 2);
    backupCtx.arc(55, 30, 8, 0, Math.PI * 2);
    backupCtx.fill();
    
    backupCtx.fillStyle = 'black';
    backupCtx.beginPath();
    backupCtx.arc(25, 30, 4, 0, Math.PI * 2);
    backupCtx.arc(55, 30, 4, 0, Math.PI * 2);
    backupCtx.fill();
    
    // 绘制鼻子
    backupCtx.fillStyle = 'pink';
    backupCtx.beginPath();
    backupCtx.arc(40, 45, 5, 0, Math.PI * 2);
    backupCtx.fill();
    
    moleImage = backupCanvas;
}

// 绘制背景
function drawBackground() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // 绘制草地背景
    ctx.fillStyle = '#90EE90';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // 绘制地鼠洞
    holes.forEach(hole => {
        ctx.fillStyle = '#654321';
        ctx.beginPath();
        ctx.ellipse(hole.x, hole.y + 20, 40, 20, 0, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.fillStyle = '#3E2723';
        ctx.beginPath();
        ctx.ellipse(hole.x, hole.y + 25, 35, 15, 0, 0, Math.PI * 2);
        ctx.fill();
    });
    
    // 绘制地鼠
    moles.forEach(mole => {
        if (mole.visible) {
            const imgSize = 60;
            const x = mole.x - imgSize/2;
            const y = mole.y - imgSize/2;
            
            // 绘制圆形裁剪的地鼠图片
            ctx.save();
            ctx.beginPath();
            ctx.arc(mole.x, mole.y, imgSize/2, 0, Math.PI * 2);
            ctx.clip();
            ctx.drawImage(moleImage, x, y, imgSize, imgSize);
            ctx.restore();
            
            // 绘制边框
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(mole.x, mole.y, imgSize/2, 0, Math.PI * 2);
            ctx.stroke();
        }
    });
}

// 开始游戏
function startGame() {
    if (gameActive) return;
    
    gameActive = true;
    score = 0;
    timeLeft = 30;
    moles = [];
    
    // 初始化地鼠
    holes.forEach(hole => {
        moles.push({
            x: hole.x,
            y: hole.y - 30,
            visible: false,
            timer: 0
        });
    });
    
    // 更新UI
    document.getElementById('score').textContent = score;
    document.getElementById('time').textContent = timeLeft;
    document.getElementById('startBtn').disabled = true;
    document.getElementById('gameOver').style.display = 'none';
    
    // 开始计时器
    gameTimer = setInterval(updateTimer, 1000);
    moleTimer = setInterval(spawnMole, 1000);
    
    // 立即生成第一个地鼠
    spawnMole();
}

// 更新计时器
function updateTimer() {
    timeLeft--;
    document.getElementById('time').textContent = timeLeft;
    
    if (timeLeft <= 0) {
        endGame();
    }
}

// 生成地鼠
function spawnMole() {
    if (!gameActive) return;
    
    // 隐藏所有地鼠
    moles.forEach(mole => {
        mole.visible = false;
    });
    
    // 随机选择一个地鼠显示
    const randomIndex = Math.floor(Math.random() * moles.length);
    moles[randomIndex].visible = true;
    moles[randomIndex].timer = Date.now();
    
    drawBackground();
}

// 处理点击事件
function handleClick(event) {
    if (!gameActive) return;
    
    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    moles.forEach(mole => {
        if (mole.visible) {
            const distance = Math.sqrt(
                Math.pow(x - mole.x, 2) + Math.pow(y - mole.y, 2)
            );
            
            if (distance < 30) {
                // 击中地鼠
                score += 10;
                document.getElementById('score').textContent = score;
                
                // 添加击中效果
                createHitEffect(mole.x, mole.y);
                
                // 隐藏地鼠
                mole.visible = false;
                drawBackground();
                
                // 立即生成新地鼠
                setTimeout(spawnMole, 300);
            }
        }
    });
}

// 创建击中效果
function createHitEffect(x, y) {
    ctx.fillStyle = 'rgba(255, 215, 0, 0.8)';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('+10', x, y - 40);
    
    setTimeout(() => {
        drawBackground();
    }, 500);
}

// 结束游戏
function endGame() {
    gameActive = false;
    clearInterval(gameTimer);
    clearInterval(moleTimer);
    
    // 隐藏所有地鼠
    moles.forEach(mole => {
        mole.visible = false;
    });
    drawBackground();
    
    // 显示游戏结束信息
    document.getElementById('finalScore').textContent = score;
    document.getElementById('gameOver').style.display = 'block';
    document.getElementById('startBtn').disabled = false;
    document.getElementById('resetBtn').disabled = false;
}

// 重置游戏
function resetGame() {
    score = 0;
    timeLeft = 30;
    document.getElementById('score').textContent = score;
    document.getElementById('time').textContent = timeLeft;
    document.getElementById('gameOver').style.display = 'none';
    document.getElementById('resetBtn').disabled = true;
    
    drawBackground();
}

// 页面加载完成后初始化游戏
window.addEventListener('load', initGame);