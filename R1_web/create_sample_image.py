from PIL import Image, ImageDraw, ImageFont
import os

# 创建一个示例的"浩然"图片作为地鼠
width, height = 200, 200

# 创建新图片
img = Image.new('RGB', (width, height), color='#FFB6C1')
draw = ImageDraw.Draw(img)

# 绘制一个简单的卡通头像
# 绘制圆形脸
face_color = '#FDBCB4'
draw.ellipse([20, 20, 180, 180], fill=face_color, outline='#FF6B6B', width=3)

# 绘制眼睛
eye_color = '#333333'
draw.ellipse([60, 70, 85, 95], fill='white', outline=eye_color, width=2)
draw.ellipse([115, 70, 140, 95], fill='white', outline=eye_color, width=2)
draw.ellipse([70, 80, 80, 90], fill=eye_color)
draw.ellipse([125, 80, 135, 90], fill=eye_color)

# 绘制鼻子
nose_color = '#FF69B4'
draw.ellipse([90, 100, 110, 115], fill=nose_color)

# 绘制嘴巴
mouth_color = '#FF1493'
draw.arc([70, 120, 130, 150], 0, 180, fill=mouth_color, width=3)

# 绘制耳朵
draw.ellipse([10, 50, 40, 90], fill=face_color, outline='#FF6B6B', width=3)
draw.ellipse([160, 50, 190, 90], fill=face_color, outline='#FF6B6B', width=3)

# 添加文字
try:
    font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 24)
except:
    font = ImageFont.load_default()

draw.text((width//2, height-30), "浩然", fill='#8B4513', font=font, anchor='mm')

# 保存图片
img.save('haoran.jpg', 'JPEG', quality=95)
print("已创建 haoran.jpg 示例图片")