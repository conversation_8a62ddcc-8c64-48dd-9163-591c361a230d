<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小球在旋转五边形内弹跳</title>
    <style>
        /* CSS: 确保canvas铺满整个页面 */
        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden; /* 隐藏滚动条 */
            background-color: #1a1a1a; /* 深色背景 */
        }
        canvas {
            display: block; /* 避免canvas下方出现小间隙 */
        }
    </style>
</head>
<body>
    <canvas id="animationCanvas"></canvas>

    <script>
        // JavaScript: 动画逻辑
        window.onload = function() {
            const canvas = document.getElementById('animationCanvas');
            const ctx = canvas.getContext('2d');

            let width, height;

            // --- 物理和动画参数 ---
            const gravity = 0.2;
            const restitution = 0.85; // 恢复系数，模拟能量损失，值越小弹跳越低

            // --- 小球对象 ---
            const ball = {
                x: 0,
                y: 0,
                radius: 15,
                color: '#ff4757',
                vx: 0, // 水平速度
                vy: 0  // 垂直速度
            };

            // --- 五边形对象 ---
            const pentagon = {
                x: 0,
                y: 0,
                radius: 0,
                sides: 5,
                rotation: 0,
                rotationSpeed: 0.002, // 顺时针旋转速度
                color: '#5352ed',
                lineWidth: 5
            };

            // --- 初始化和响应式布局 ---
            function setup() {
                width = canvas.width = window.innerWidth;
                height = canvas.height = window.innerHeight;

                // 将原点设置在画布中心
                pentagon.x = width / 2;
                pentagon.y = height / 2;
                
                // 五边形半径为窗口短边的40%
                pentagon.radius = Math.min(width, height) * 0.4;

                // 初始化小球位置和速度
                ball.x = pentagon.x;
                ball.y = pentagon.y - pentagon.radius / 2;
                ball.vx = (Math.random() - 0.5) * 5; // 随机初始水平速度
                ball.vy = 1;
            }

            // 监听窗口大小变化
            window.addEventListener('resize', setup);

            // --- 绘图函数 ---
            function drawPentagon() {
                ctx.save();
                ctx.translate(pentagon.x, pentagon.y);
                ctx.rotate(pentagon.rotation);
                
                ctx.beginPath();
                for (let i = 0; i < pentagon.sides; i++) {
                    const angle = (i / pentagon.sides) * Math.PI * 2 - Math.PI / 2;
                    const x = pentagon.radius * Math.cos(angle);
                    const y = pentagon.radius * Math.sin(angle);
                    if (i === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                ctx.closePath();

                ctx.strokeStyle = pentagon.color;
                ctx.lineWidth = pentagon.lineWidth;
                ctx.stroke();
                ctx.restore();
            }

            function drawBall() {
                ctx.beginPath();
                ctx.arc(ball.x, ball.y, ball.radius, 0, Math.PI * 2);
                ctx.fillStyle = ball.color;
                ctx.fill();
                ctx.closePath();
            }

            // --- 碰撞检测与处理 ---
            function handleCollision() {
                const vertices = [];
                // 计算旋转后五边形的顶点坐标
                for (let i = 0; i < pentagon.sides; i++) {
                    const angle = (i / pentagon.sides) * Math.PI * 2 - Math.PI / 2 + pentagon.rotation;
                    vertices.push({
                        x: pentagon.x + pentagon.radius * Math.cos(angle),
                        y: pentagon.y + pentagon.radius * Math.sin(angle)
                    });
                }

                // 遍历五边形的每一条边
                for (let i = 0; i < pentagon.sides; i++) {
                    const p1 = vertices[i];
                    const p2 = vertices[(i + 1) % pentagon.sides];

                    // 计算小球中心到边的向量
                    const ac = { x: ball.x - p1.x, y: ball.y - p1.y };
                    const ab = { x: p2.x - p1.x, y: p2.y - p1.y };
                    
                    const ab2 = ab.x * ab.x + ab.y * ab.y;
                    const acab = ac.x * ab.x + ac.y * ab.y;
                    let t = acab / ab2;
                    
                    // 找到边上离球心最近的点
                    t = Math.max(0, Math.min(1, t));
                    const closestPoint = {
                        x: p1.x + t * ab.x,
                        y: p1.y + t * ab.y
                    };

                    const dist_x = ball.x - closestPoint.x;
                    const dist_y = ball.y - closestPoint.y;
                    const distance = Math.sqrt(dist_x * dist_x + dist_y * dist_y);

                    // 如果距离小于半径，则发生碰撞
                    if (distance < ball.radius) {
                        // 1. 将小球移出边界，防止卡住
                        const overlap = ball.radius - distance;
                        ball.x += (dist_x / distance) * overlap;
                        ball.y += (dist_y / distance) * overlap;

                        // 2. 计算边的法向量 (用于反弹)
                        let normal_x = ab.y;
                        let normal_y = -ab.x;
                        
                        // 确保法向量指向五边形内部
                        const d_center = (pentagon.x - p1.x) * normal_x + (pentagon.y - p1.y) * normal_y;
                        if (d_center > 0) {
                            normal_x *= -1;
                            normal_y *= -1;
                        }

                        const len = Math.sqrt(normal_x * normal_x + normal_y * normal_y);
                        normal_x /= len;
                        normal_y /= len;

                        // 3. 计算速度在法向量上的投影
                        const dot = ball.vx * normal_x + ball.vy * normal_y;

                        // 4. 计算反弹后的速度 (向量反射)
                        ball.vx = (ball.vx - 2 * dot * normal_x) * restitution;
                        ball.vy = (ball.vy - 2 * dot * normal_y) * restitution;
                    }
                }
            }

            // --- 动画主循环 ---
            function animate() {
                // 清空画布
                ctx.clearRect(0, 0, width, height);

                // 更新五边形旋转
                pentagon.rotation += pentagon.rotationSpeed;

                // 更新小球状态
                ball.vy += gravity; // 应用重力
                ball.x += ball.vx;
                ball.y += ball.vy;

                // 处理碰撞
                handleCollision();

                // 绘制
                drawPentagon();
                drawBall();

                // 请求下一帧
                requestAnimationFrame(animate);
            }

            // --- 启动动画 ---
            setup();
            animate();
        };
    </script>
</body>
</html>