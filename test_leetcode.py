#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from typing import List, Optional
import unittest
import heapq


#给你一个 只包含正整数 的 非空 数组 nums 。请你判断是否可以将这个数组分割成两个子集，使得两个子集的元素和相等。

class Solution:
    def canPartition(self, nums: List[int]) -> bool:
        if not nums:  # 处理空数组情况
            return False
            
        total_sum = sum(nums)
        if total_sum % 2 != 0:
            return False

        target_sum = total_sum // 2
        n = len(nums)

        # 使用动态规划数组来存储状态
        dp = [False] * (target_sum + 1)
        dp[0] = True

        for num in nums:
            for j in range(target_sum, num - 1, -1):
                dp[j] = dp[j] or dp[j - num]

        return dp[target_sum]

class TestCanPartition(unittest.TestCase):
    def setUp(self):
        self.solution = Solution()

    def test_can_partition(self):
        nums = [1, 5, 11, 5]
        self.assertTrue(self.solution.canPartition(nums))

    def test_cannot_partition(self):
        nums = [1, 2, 3, 5]
        self.assertFalse(self.solution.canPartition(nums))

    def test_empty_list(self):
        nums = []
        self.assertFalse(self.solution.canPartition(nums))

    def test_single_element(self):
        nums = [1]
        self.assertFalse(self.solution.canPartition(nums))

if __name__ == '__main__':
    unittest.main()