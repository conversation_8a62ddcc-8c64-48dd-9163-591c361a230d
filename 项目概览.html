<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目全景概览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .module-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
        }
        
        .module-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--accent-color);
        }
        
        .module-card.game {
            --accent-color: #FF6B6B;
        }
        
        .module-card.analysis {
            --accent-color: #4ECDC4;
        }
        
        .module-card.video {
            --accent-color: #45B7D1;
        }
        
        .module-card.frontend {
            --accent-color: #96CEB4;
        }
        
        .module-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            display: block;
        }
        
        .module-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2C3E50;
        }
        
        .module-description {
            color: #7F8C8D;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .features-list {
            list-style: none;
        }
        
        .features-list li {
            padding: 5px 0;
            color: #34495E;
            position: relative;
            padding-left: 20px;
        }
        
        .features-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: var(--accent-color);
            font-weight: bold;
        }
        
        .tech-stack {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 40px;
        }
        
        .tech-title {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 20px;
            color: #2C3E50;
            text-align: center;
        }
        
        .tech-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .tech-category {
            text-align: center;
            padding: 20px;
            background: #F8F9FA;
            border-radius: 10px;
            transition: background 0.3s ease;
        }
        
        .tech-category:hover {
            background: #E9ECEF;
        }
        
        .tech-category h4 {
            color: #495057;
            margin-bottom: 10px;
        }
        
        .tech-items {
            font-size: 0.9rem;
            color: #6C757D;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            display: block;
        }
        
        .stat-label {
            color: #7F8C8D;
            font-size: 0.9rem;
        }
        
        .footer {
            text-align: center;
            color: white;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .modules-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 项目全景概览</h1>
            <p>多功能综合性项目 - 游戏、数据分析、视频处理、前端设计</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <span class="stat-number">5</span>
                <span class="stat-label">游戏模块</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">4</span>
                <span class="stat-label">数据分析工具</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">7</span>
                <span class="stat-label">前端界面</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">10+</span>
                <span class="stat-label">技术栈</span>
            </div>
        </div>
        
        <div class="modules-grid">
            <div class="module-card game">
                <span class="module-icon">🎮</span>
                <h3 class="module-title">游戏娱乐模块</h3>
                <p class="module-description">包含多种经典游戏和互动娱乐应用，提供丰富的用户体验。</p>
                <ul class="features-list">
                    <li>打浩然地鼠游戏</li>
                    <li>五子棋对战</li>
                    <li>经典扫雷</li>
                    <li>俄罗斯方块</li>
                    <li>播客应用原型</li>
                </ul>
            </div>
            
            <div class="module-card analysis">
                <span class="module-icon">📊</span>
                <h3 class="module-title">数据分析模块</h3>
                <p class="module-description">强大的数据处理和分析能力，支持多维度用户行为分析。</p>
                <ul class="features-list">
                    <li>eplus使用数据分析</li>
                    <li>视频质量评估</li>
                    <li>在线QoE评估</li>
                    <li>用户行为洞察</li>
                </ul>
            </div>
            
            <div class="module-card video">
                <span class="module-icon">🎬</span>
                <h3 class="module-title">视频处理模块</h3>
                <p class="module-description">完整的视频处理解决方案，从下载到质量评估的全流程支持。</p>
                <ul class="features-list">
                    <li>视频下载工具</li>
                    <li>质量评估服务</li>
                    <li>批量处理能力</li>
                    <li>多格式支持</li>
                </ul>
            </div>
            
            <div class="module-card frontend">
                <span class="module-icon">📱</span>
                <h3 class="module-title">前端界面设计</h3>
                <p class="module-description">现代化的用户界面设计，响应式布局，跨平台兼容。</p>
                <ul class="features-list">
                    <li>播客应用完整UI</li>
                    <li>游戏界面设计</li>
                    <li>响应式布局</li>
                    <li>移动端适配</li>
                </ul>
            </div>
        </div>
        
        <div class="tech-stack">
            <h3 class="tech-title">技术栈全景</h3>
            <div class="tech-categories">
                <div class="tech-category">
                    <h4>前端技术</h4>
                    <div class="tech-items">HTML5, CSS3, JavaScript ES6+, Canvas API, Tailwind CSS</div>
                </div>
                <div class="tech-category">
                    <h4>后端技术</h4>
                    <div class="tech-items">Python 3.x, Flask, Requests, Pandas, NumPy</div>
                </div>
                <div class="tech-category">
                    <h4>数据分析</h4>
                    <div class="tech-items">Matplotlib, Seaborn, Excel/CSV, JSON</div>
                </div>
                <div class="tech-category">
                    <h4>机器学习</h4>
                    <div class="tech-items">CenseoQoE算法, 视频质量评估, 用户行为分析</div>
                </div>
                <div class="tech-category">
                    <h4>工具配置</h4>
                    <div class="tech-items">FFmpeg, Git, 环境配置, 依赖管理</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>© 2024 多功能综合性项目 - 游戏、数据分析、视频处理、前端设计的完美融合</p>
        </div>
    </div>
</body>
</html>