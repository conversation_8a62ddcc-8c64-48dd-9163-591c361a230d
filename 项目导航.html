<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目导航中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .nav-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .nav-header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .nav-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
        }
        
        .nav-item {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            color: inherit;
            display: block;
        }
        
        .nav-item:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        
        .nav-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            display: block;
        }
        
        .nav-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2C3E50;
        }
        
        .nav-description {
            color: #7F8C8D;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .nav-item.game { border-top: 4px solid #FF6B6B; }
        .nav-item.analysis { border-top: 4px solid #4ECDC4; }
        .nav-item.video { border-top: 4px solid #45B7D1; }
        .nav-item.prototype { border-top: 4px solid #96CEB4; }
        .nav-item.document { border-top: 4px solid #FECA57; }
        .nav-item.overview { border-top: 4px solid #DDA0DD; }
        
        .status-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #27AE60;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
        }
        
        @media (max-width: 768px) {
            .nav-header h1 {
                font-size: 2rem;
            }
            
            .nav-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="nav-container">
        <div class="nav-header">
            <h1>🎯 项目导航中心</h1>
            <p>快速访问项目各个模块和功能</p>
        </div>
        
        <div class="nav-grid">
            <a href="R1_web/index.html" class="nav-item game" target="_blank">
                <span class="nav-icon">🎯</span>
                <div class="nav-title">打地鼠游戏</div>
                <div class="nav-description">经典打地鼠游戏，使用真实照片作为地鼠形象</div>
                <span class="status-badge">可玩</span>
            </a>
            
            <a href="game/index.html" class="nav-item game" target="_blank">
                <span class="nav-icon">🎮</span>
                <div class="nav-title">经典游戏合集</div>
                <div class="nav-description">五子棋、扫雷、俄罗斯方块等经典游戏</div>
                <span class="status-badge">可玩</span>
            </a>
            
            <a href="test_prompt/index.html" class="nav-item prototype" target="_blank">
                <span class="nav-icon">📱</span>
                <div class="nav-title">播客应用原型</div>
                <div class="nav-description">完整的播客应用界面设计，7个核心页面</div>
                <span class="status-badge">可预览</span>
            </a>
            
            <a href="项目概览.html" class="nav-item overview" target="_blank">
                <span class="nav-icon">🚀</span>
                <div class="nav-title">项目全景概览</div>
                <div class="nav-description">项目整体介绍和功能模块展示</div>
                <span class="status-badge">新</span>
            </a>
            
            <div class="nav-item analysis" onclick="runDataAnalysis()">
                <span class="nav-icon">📊</span>
                <div class="nav-title">数据分析工具</div>
                <div class="nav-description">eplus使用数据分析，生成可视化报告</div>
                <span class="status-badge">Python</span>
            </div>
            
            <div class="nav-item video" onclick="showVideoTools()">
                <span class="nav-icon">🎬</span>
                <div class="nav-title">视频处理工具</div>
                <div class="nav-description">视频质量评估和下载工具集合</div>
                <span class="status-badge">工具</span>
            </div>
            
            <a href="项目介绍.md" class="nav-item document" target="_blank">
                <span class="nav-icon">📋</span>
                <div class="nav-title">项目文档</div>
                <div class="nav-description">详细的项目介绍和技术文档</div>
                <span class="status-badge">必读</span>
            </a>
            
            <div class="nav-item document" onclick="showTechStack()">
                <span class="nav-icon">⚙️</span>
                <div class="nav-title">技术栈</div>
                <div class="nav-description">项目使用的技术栈和工具介绍</div>
                <span class="status-badge">技术</span>
            </div>
        </div>
    </div>

    <script>
        function runDataAnalysis() {
            alert('数据分析功能需要运行：\n\npython data_analysis.py\n\n请确保已安装必要的Python依赖包。');
        }
        
        function showVideoTools() {
            alert('视频处理工具包括：\n\n• QualityGrade.py - 视频质量评分\n• CenseoQoE-Server_predict_online.py - 在线质量评估\n• get_kg_flv/ - 视频下载工具\n\n请查看相应Python文件获取使用说明。');
        }
        
        function showTechStack() {
            alert('技术栈包括：\n\n前端：HTML5, CSS3, JavaScript ES6+, Canvas API, Tailwind CSS\n后端：Python 3.x, Flask, Requests, Pandas, NumPy\n数据分析：Matplotlib, Seaborn, Excel/CSV, JSON\n机器学习：CenseoQoE算法, 视频质量评估\n工具：FFmpeg, Git, 环境配置');
        }
        
        // 添加键盘导航支持
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                window.close();
            }
        });
    </script>
</body>
</html>