<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跨星际高铁 - 未来交通的终极形态</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&family=Noto+Serif+SC:wght@400;500;700&display=swap');
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            overflow-x: hidden;
        }
        
        .hero-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .star-field {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(2px 2px at 20px 30px, #eee, transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
                radial-gradient(1px 1px at 90px 40px, #fff, transparent),
                radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
                radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
            background-repeat: repeat;
            background-size: 200px 100px;
            animation: sparkle 20s linear infinite;
            opacity: 0.6;
        }
        
        @keyframes sparkle {
            from { transform: translateY(0px); }
            to { transform: translateY(-100px); }
        }
        
        .magazine-title {
            font-family: 'Noto Serif SC', serif;
            font-weight: 900;
            font-style: italic;
            background: linear-gradient(45deg, #fff, #e0e7ff, #fff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
        }
        
        .content-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        
        .content-card:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
        }
        
        .rail-animation {
            position: relative;
            overflow: hidden;
        }
        
        .rail-animation::before {
            content: '';
            position: absolute;
            top: 50%;
            left: -100%;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, #667eea, transparent);
            animation: rail-move 3s linear infinite;
        }
        
        @keyframes rail-move {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        .particle-trail {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #667eea;
            border-radius: 50%;
            animation: particle-float 4s ease-in-out infinite;
        }
        
        @keyframes particle-float {
            0%, 100% { 
                transform: translateY(0px) scale(1); 
                opacity: 1;
            }
            50% { 
                transform: translateY(-20px) scale(1.5); 
                opacity: 0.5;
            }
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -8px;
            top: 8px;
            width: 16px;
            height: 16px;
            background: #667eea;
            border-radius: 50%;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.8);
        }
        
        .scroll-reveal {
            opacity: 0;
            transform: translateY(50px);
            transition: all 0.8s ease;
        }
        
        .scroll-reveal.revealed {
            opacity: 1;
            transform: translateY(0);
        }
        
        .magazine-quote {
            font-family: 'Noto Serif SC', serif;
            font-style: italic;
            position: relative;
        }
        
        .magazine-quote::before {
            content: '"';
            font-size: 4rem;
            position: absolute;
            top: -1rem;
            left: -2rem;
            color: #667eea;
            opacity: 0.3;
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero-gradient min-h-screen flex items-center justify-center relative overflow-hidden">
        <div class="star-field"></div>
        <div class="absolute inset-0 bg-black opacity-20"></div>
        
        <div class="relative z-10 text-center px-4 max-w-6xl mx-auto">
            <div class="mb-8">
                <h1 class="magazine-title text-5xl md:text-7xl lg:text-8xl mb-4 leading-tight">
                    跨星际高铁
                </h1>
                <p class="text-xl md:text-2xl lg:text-3xl text-blue-200 font-light italic">
                    穿越光年的旅程，从此刻开始
                </p>
            </div>
            
            <div class="max-w-3xl mx-auto mb-12">
                <p class="text-lg md:text-xl text-blue-100 leading-relaxed opacity-90">
                    从地球到火星，从太阳系到比邻星，
                    <br class="hidden md:block">
                    重新定义星际旅行的速度与优雅
                </p>
            </div>
            
            <div class="rail-animation bg-white bg-opacity-10 rounded-full h-px mx-auto max-w-2xl mb-8"></div>
            
            <button id="startJourney" class="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-bold py-4 px-8 rounded-full text-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                <i class="fas fa-rocket mr-2"></i>
                开启星际之旅
            </button>
        </div>
        
        <!-- Floating particles -->
        <div class="particle-trail" style="top: 20%; left: 10%; animation-delay: 0s;"></div>
        <div class="particle-trail" style="top: 60%; left: 80%; animation-delay: 1s;"></div>
        <div class="particle-trail" style="top: 40%; left: 30%; animation-delay: 2s;"></div>
        <div class="particle-trail" style="top: 80%; left: 60%; animation-delay: 3s;"></div>
    </section>

    <!-- Main Content -->
    <main class="py-20 px-4">
        <!-- Feature Article -->
        <section class="max-w-6xl mx-auto mb-20">
            <div class="grid md:grid-cols-2 gap-12 items-center">
                <div class="scroll-reveal">
                    <h2 class="text-4xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">
                        革命性的交通技术
                    </h2>
                    <p class="text-gray-300 mb-4 leading-relaxed">
                        跨星际高铁采用量子纠缠推进技术，利用时空折叠原理实现超光速旅行。
                        每节车厢都是一个独立的生态舱，配备重力调节系统和生命维持装置。
                    </p>
                    <p class="text-gray-400 mb-6 leading-relaxed">
                        从地球到火星仅需40分钟，到比邻星只需3.5小时，让星际旅行变得如地铁通勤般便捷。
                    </p>
                    <div class="flex items-center space-x-4 text-blue-300">
                        <div class="flex items-center">
                            <i class="fas fa-clock mr-2"></i>
                            <span>40分钟到火星</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-tachometer-alt mr-2"></i>
                            <span>0.1c巡航速度</span>
                        </div>
                    </div>
                </div>
                
                <div class="scroll-reveal">
                    <div class="content-card rounded-2xl p-8">
                        <div class="aspect-video bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl mb-4 relative overflow-hidden">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <i class="fas fa-train text-6xl text-white opacity-50"></i>
                            </div>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">技术规格</h3>
                        <ul class="text-sm text-gray-400 space-y-1">
                            <li>• 量子纠缠推进器</li>
                            <li>• 时空折叠稳定器</li>
                            <li>• 生态循环生命系统</li>
                            <li>• AI智能导航</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Timeline -->
        <section class="max-w-6xl mx-auto mb-20">
            <h2 class="text-4xl font-bold text-center mb-12 scroll-reveal">
                发展时间线
            </h2>
            
            <div class="relative">
                <div class="absolute left-4 md:left-1/2 top-0 bottom-0 w-px bg-gradient-to-b from-blue-500 to-purple-500"></div>
                
                <div class="space-y-12">
                    <div class="timeline-item relative pl-12 md:pl-0 md:flex md:items-center scroll-reveal">
                        <div class="md:w-1/2 md:pr-8 md:text-right">
                            <div class="content-card rounded-xl p-6">
                                <h3 class="text-xl font-semibold mb-2 text-blue-400">2025年</h3>
                                <p class="text-gray-300">原型机首次测试</p>
                            </div>
                        </div>
                        <div class="hidden md:block md:w-1/2"></div>
                    </div>
                    
                    <div class="timeline-item relative pl-12 md:pl-0 md:flex md:items-center scroll-reveal">
                        <div class="hidden md:block md:w-1/2"></div>
                        <div class="md:w-1/2 md:pl-8">
                            <div class="content-card rounded-xl p-6">
                                <h3 class="text-xl font-semibold mb-2 text-purple-400">2030年</h3>
                                <p class="text-gray-300">地球-火星航线开通</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="timeline-item relative pl-12 md:pl-0 md:flex md:items-center scroll-reveal">
                        <div class="md:w-1/2 md:pr-8 md:text-right">
                            <div class="content-card rounded-xl p-6">
                                <h3 class="text-xl font-semibold mb-2 text-blue-400">2035年</h3>
                                <p class="text-gray-300">太阳系网络建成</p>
                            </div>
                        </div>
                        <div class="hidden md:block md:w-1/2"></div>
                    </div>
                    
                    <div class="timeline-item relative pl-12 md:pl-0 md:flex md:items-center scroll-reveal">
                        <div class="hidden md:block md:w-1/2"></div>
                        <div class="md:w-1/2 md:pl-8">
                            <div class="content-card rounded-xl p-6">
                                <h3 class="text-xl font-semibold mb-2 text-purple-400">2040年</h3>
                                <p class="text-gray-300">比邻星航线开通</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quote Section -->
        <section class="max-w-4xl mx-auto mb-20">
            <div class="content-card rounded-2xl p-12 text-center scroll-reveal">
                <blockquote class="magazine-quote text-2xl md:text-3xl text-gray-200 mb-6 leading-relaxed">
                    我们不只是在建造交通工具，我们在编织连接星球的纽带，
                    让人类文明的光芒照亮整个银河系。
                </blockquote>
                <cite class="text-blue-300 font-semibold">— 陈星航，星际高铁总工程师</cite>
            </div>
        </section>

        <!-- Features Grid -->
        <section class="max-w-6xl mx-auto mb-20">
            <h2 class="text-4xl font-bold text-center mb-12 scroll-reveal">
                核心特色
            </h2>
            
            <div class="grid md:grid-cols-3 gap-8">
                <div class="content-card rounded-2xl p-8 text-center scroll-reveal">
                    <div class="text-4xl mb-4 text-blue-400">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-4">零事故保障</h3>
                    <p class="text-gray-400">AI智能防撞系统，量子纠缠通信确保绝对安全</p>
                </div>
                
                <div class="content-card rounded-2xl p-8 text-center scroll-reveal">
                    <div class="text-4xl mb-4 text-purple-400">
                        <i class="fas fa-leaf"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-4">生态友好</h3>
                    <p class="text-gray-400">零排放推进技术，完全可循环的车厢材料</p>
                </div>
                
                <div class="content-card rounded-2xl p-8 text-center scroll-reveal">
                    <div class="text-4xl mb-4 text-blue-400">
                        <i class="fas fa-couch"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-4">豪华体验</h3>
                    <p class="text-gray-400">五星级舱室，全息娱乐，重力调节座椅</p>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="max-w-4xl mx-auto text-center">
            <div class="scroll-reveal">
                <h2 class="text-4xl font-bold mb-6">
                    准备好开始您的星际之旅了吗？
                </h2>
                <p class="text-xl text-gray-300 mb-8">
                    成为第一批体验跨星际高铁的乘客
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-4 px-8 rounded-full text-lg transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-ticket-alt mr-2"></i>
                        预订船票
                    </button>
                    <button class="border border-blue-500 text-blue-400 hover:bg-blue-500 hover:text-white font-bold py-4 px-8 rounded-full text-lg transition-all duration-300">
                        <i class="fas fa-info-circle mr-2"></i>
                        了解更多
                    </button>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="py-12 px-4 border-t border-gray-800">
        <div class="max-w-6xl mx-auto text-center text-gray-400">
            <p>&copy; 2024 跨星际高铁集团. 让星空不再遥远.</p>
        </div>
    </footer>

    <script>
        // Scroll reveal animation
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('revealed');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.scroll-reveal').forEach(el => {
            observer.observe(el);
        });

        // 开启星际之旅按钮功能
        document.getElementById('startJourney').addEventListener('click', () => {
            // 创建模态窗口
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            `;
            
            modal.innerHTML = `
                <div style="
                    background: linear-gradient(135deg, #1a1a2e, #16213e);
                    border: 1px solid #667eea;
                    border-radius: 20px;
                    padding: 40px;
                    max-width: 500px;
                    text-align: center;
                    box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
                    animation: slideUp 0.3s ease;
                ">
                    <h3 style="color: #667eea; font-size: 24px; margin-bottom: 20px;">
                        <i class="fas fa-rocket" style="margin-right: 10px;"></i>
                        欢迎登舰！
                    </h3>
                    <p style="color: #e0e7ff; margin-bottom: 30px; line-height: 1.6;">
                        恭喜您！您已成功预订前往火星的星际高铁。
                        <br>
                        预计出发时间：2024年12月31日 23:59
                        <br>
                        座位号：A7-42（靠窗座位）
                    </p>
                    <button onclick="this.parentElement.parentElement.remove()" style="
                        background: linear-gradient(135deg, #667eea, #764ba2);
                        color: white;
                        border: none;
                        padding: 12px 30px;
                        border-radius: 25px;
                        font-size: 16px;
                        cursor: pointer;
                        transition: transform 0.2s;
                    " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                        确认登舰
                    </button>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // 点击背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        });

        // 预订船票按钮功能
        document.querySelectorAll('button').forEach(button => {
            if (button.innerHTML.includes('预订船票')) {
                button.addEventListener('click', () => {
                    alert('🎫 正在跳转到预订系统...\n\n请选择您的目的地：\n• 火星殖民地 - 40分钟\n• 月球基地 - 15分钟\n• 土卫六站 - 2小时30分钟');
                });
            }
            
            if (button.innerHTML.includes('了解更多')) {
                button.addEventListener('click', () => {
                    const infoSection = document.createElement('div');
                    infoSection.style.cssText = `
                        position: fixed;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        background: rgba(26, 26, 46, 0.95);
                        border: 1px solid #667eea;
                        border-radius: 15px;
                        padding: 30px;
                        max-width: 600px;
                        max-height: 80vh;
                        overflow-y: auto;
                        z-index: 1000;
                        color: white;
                    `;
                    
                    infoSection.innerHTML = `
                        <h3 style="color: #667eea; margin-bottom: 20px;">跨星际高铁详细介绍</h3>
                        <div style="color: #e0e7ff; line-height: 1.8;">
                            <h4>🚀 技术原理</h4>
                            <p>采用量子纠缠推进技术，利用时空折叠原理实现超光速旅行。通过扭曲时空结构，创造出"虫洞"通道，让列车能够在短时间内跨越星际距离。</p>
                            
                            <h4>🛡️ 安全保障</h4>
                            <p>配备AI智能导航系统，实时监测宇宙环境变化。每节车厢都有独立的生态循环系统，确保乘客安全。</p>
                            
                            <h4>💺 乘坐体验</h4>
                            <p>豪华太空舱设计，配备全息娱乐系统、重力调节座椅、观景窗。提供太空餐食和失重体验。</p>
                            
                            <button onclick="this.parentElement.parentElement.remove()" style="
                                background: #667eea;
                                color: white;
                                border: none;
                                padding: 10px 20px;
                                border-radius: 20px;
                                margin-top: 20px;
                                cursor: pointer;
                            ">关闭</button>
                        </div>
                    `;
                    
                    document.body.appendChild(infoSection);
                });
            }
        });

        // Add hover effects to cards
        document.querySelectorAll('.content-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-5px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Button click effects
        document.querySelectorAll('button').forEach(button => {
            button.addEventListener('click', (e) => {
                const ripple = document.createElement('span');
                ripple.classList.add('ripple');
                const rect = button.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(255,255,255,0.3);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s ease-out;
                    pointer-events: none;
                `;
                
                button.style.position = 'relative';
                button.style.overflow = 'hidden';
                button.appendChild(ripple);
                
                setTimeout(() => ripple.remove(), 600);
            });
        });

        // Add animation CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
            
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            
            @keyframes slideUp {
                from { 
                    opacity: 0;
                    transform: translateY(30px);
                }
                to { 
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>